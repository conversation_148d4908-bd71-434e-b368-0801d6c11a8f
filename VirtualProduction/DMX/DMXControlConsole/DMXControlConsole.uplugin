{
	"FileVersion": 3,
	"Version": 1,
	"VersionName": "1.0",
	"FriendlyName": "DMX Control Console",
	"Description": "Console that can be patched from DMX Libraries and sends DMX to Output Ports",
	"Category": "Virtual Production",
	"CreatedBy": "Epic Games, Inc.",
	"CreatedByURL": "https://epicgames.com",
	"DocsURL": "",
	"MarketplaceURL": "",
	"SupportURL": "",
	"CanContainContent": false,
	"IsBetaVersion": false,
	"Modules": [
		{
			"Name": "DMXControlConsole",
			"Type": "Runtime",
			"LoadingPhase": "Default"
		},
		{
			"Name": "DMXControlConsoleEditor",
			"Type": "Editor",
			"LoadingPhase": "Default"
		},
	],
	"Plugins": [
		{
			"Name": "DMXProtocol",
			"Enabled": true
		},
		{
			"Name": "DMXEngine",
			"Enabled": true
		}
	],
	"IsExperimentalVersion": false,
	"Installed": false
}