#pragma once

#include "CoreMinimal.h"
#include "BiomeMapper.h"
#include "DynamicChangeSystem.h"
#include "LayerScalingSystem.h"
#include "NarrativeLayerGenerator.h"
#include "Performance/PerformanceOptimizer.h"
#include "PhysicalLayerGenerator.h"
#include "RoadGenerator.h"
#include "UndergroundSystem.h"
#include "Engine/Engine.h"
#include "MapGenerator.generated.h"





/**
 * 地图生成阶段枚举
 */
/**
 * 地图生成结果结构体
 */


USTRUCT(BlueprintType)
struct GAME_API FMapGenerationResult
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Result")
    TArray<FMapCell> MapCells;          // 生成的地图格子

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Result")
    int32 Width = 0;                    // 地图宽度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Result")
    int32 Height = 0;                   // 地图高度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Result")
    EMapLayer LayerType = EMapLayer::World; // 地图层级

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Result")
    TArray<FRegionShape> Regions;       // 识别出的区域

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Result")
    FRoadNetwork RoadNetwork;           // 道路网络

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Result")
    FUnifiedUndergroundNetwork UndergroundNetwork; // 地下空间网络

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Result")
    float GenerationTime = 0.0f;        // 生成耗时

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Result")
    bool bGenerationSuccess = false;    // 是否生成成功

    FMapGenerationResult()
    {
        Width = 0;
        Height = 0;
        LayerType = EMapLayer::World;
        GenerationTime = 0.0f;
        bGenerationSuccess = false;
    }
};
/**
 * 地图生成参数结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FMapGenerationParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    int32 MapWidth = 25;                // 【修复】地图宽度改为合理尺寸25x25=625格子

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    int32 MapHeight = 25;               // 【修复】地图高度改为合理尺寸25x25=625格子

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    EMapLayer TargetLayer = EMapLayer::World; // 目标层级

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    int32 Seed = 0;                     // 随机种子

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    FPhysicalGenerationParams PhysicalParams; // 物理层参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    FBiomeMappingParams BiomeParams;    // 生态区参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    FNarrativeGenerationParams NarrativeParams; // 叙事层参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    FRoadGenerationParams RoadParams;   // 道路生成参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    FUndergroundGenerationParams UndergroundParams; // 地下空间参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    FScalingParams ScalingParams;       // 缩放参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    FDynamicChangeParams DynamicParams; // 动态变化参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    FPerformanceOptimizationParams OptimizationParams; // 优化参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Generation Params")
    bool bEnableProgressCallback = true; // 是否启用进度回调

    FMapGenerationParams()
    {
        MapWidth = 25;                  // 【修复】构造函数也使用合理尺寸25x25=625格子
        MapHeight = 25;                 // 【修复】构造函数也使用合理尺寸25x25=625格子
        TargetLayer = EMapLayer::World;
        Seed = 0;
        bEnableProgressCallback = true;
    }
};


DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMapGenerationProgress, EMapGenerationPhase, Phase, float, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMapGenerationComplete, const FMapGenerationResult&, Result);

/**
 * 地图生成器
 * 统一管理所有地图生成子系统，实现完整的地图生成流程
 */
UCLASS(BlueprintType, Blueprintable)
class GAME_API UMapGenerator : public UObject
{
    GENERATED_BODY()

public:
    UMapGenerator();

    // ========== 主要生成函数 ==========
    
    /**
     * 生成完整地图
     * @param Params 生成参数
     * @return 生成结果
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    FMapGenerationResult GenerateMap(const FMapGenerationParams& Params);

    /**
     * 异步生成地图
     * @param Params 生成参数
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    void GenerateMapAsync(const FMapGenerationParams& Params);

    /**
     * 从父层级生成子层级地图
     * @param ParentResult 父层级生成结果
     * @param TargetRegion 目标区域
     * @param TargetLayer 目标层级
     * @param Params 生成参数
     * @return 子层级生成结果
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    FMapGenerationResult GenerateChildMap(const FMapGenerationResult& ParentResult, 
                                         const FRegionShape& TargetRegion,
                                         EMapLayer TargetLayer,
                                         const FMapGenerationParams& Params);

    // ========== 格子血条计算 ==========
    
    /**
     * 计算格子血条
     * @param Cell 地图格子
     * @param LayerType 地图层级
     * @param HealthParams 血条参数
     * @return 计算出的血条值
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    static float CalculateCellHealth(const FMapCell& Cell, EMapLayer LayerType, 
                                    const FCellHealthParams& HealthParams);

    /**
     * 批量更新格子血条
     * @param MapCells 地图格子数组（输入输出）
     * @param LayerType 地图层级
     * @param HealthParams 血条参数
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    static void UpdateCellsHealth(TArray<FMapCell>& MapCells, EMapLayer LayerType,
                                 const FCellHealthParams& HealthParams);

    // ========== 区域识别和管理 ==========
    
    /**
     * 识别地图中的不规则区域，并为格子赋值区域ID
     * @param MapCells 地图格子数组（会被修改，格子的RegionID会被设置）
     * @param Width 地图宽度
     * @param Height 地图高度
     * @param LayerType 地图层级
     * @return 识别出的区域数组
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    TArray<FRegionShape> IdentifyMapRegions(TArray<FMapCell>& MapCells, int32 Width, int32 Height,
                                           EMapLayer LayerType);

    /**
     * 验证区域的连通性
     * @param Region 区域形状
     * @param MapCells 地图格子数组
     * @param Width 地图宽度
     * @param Height 地图高度
     * @return 是否连通
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    static bool ValidateRegionConnectivity(const FRegionShape& Region, const TArray<FMapCell>& MapCells,
                                          int32 Width, int32 Height);

    /**
     * 检查指定位置是否为区域入口
     * @param Position 检查位置
     * @param Regions 区域数组
     * @param OutRegion 输出找到的区域
     * @return 是否为区域入口
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    static bool IsRegionEntrance(const FIntPoint& Position, const TArray<FRegionShape>& Regions,
                                FRegionShape& OutRegion);

    /**
     * 获取区域的所有入口格子
     * @param Region 区域形状
     * @return 所有入口格子位置
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    static TArray<FIntPoint> GetRegionEntrances(const FRegionShape& Region);

    // ========== 事件回调 ==========
    
    UPROPERTY(BlueprintAssignable, Category = "Map Generator")
    FOnMapGenerationProgress OnGenerationProgress;

    UPROPERTY(BlueprintAssignable, Category = "Map Generator")
    FOnMapGenerationComplete OnGenerationComplete;

    // ========== 默认配置 ==========
    
    /**
     * 获取默认生成参数
     * @param LayerType 地图层级
     * @return 默认参数
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    static FMapGenerationParams GetDefaultGenerationParams(EMapLayer LayerType);

    /**
     * 获取默认血条参数
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    static FCellHealthParams GetDefaultHealthParams();

    /**
     * 验证地图生成参数的有效性
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    static bool ValidateGenerationParams(const FMapGenerationParams& Params, FString& OutErrorMessage);

    // ========== 可视化集成函数 ==========

    /**
     * 启用地图可视化
     * @param bEnable 是否启用
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    void EnableMapVisualization(bool bEnable = true);

    /**
     * 设置地图可视化参数
     * @param VisParams 可视化参数
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    void SetVisualizationParams(const struct FVisualizationParams& VisParams);

    /**
     * 获取可视化系统
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    class UMapVisualizationSystem* GetVisualizationSystem() const;

    /**
     * 设置默认渲染组件类型（蓝图可设置）
     * @param ComponentClass 渲染组件类型
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    void SetDefaultRenderComponentClass(TSubclassOf<class USceneComponent> ComponentClass);

    /**
     * 为指定层级生成渲染组件
     * @param LayerType 层级类型
     * @param ComponentClass 渲染组件类型（可选）
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    void GenerateRenderComponentsForLayer(EMapLayer LayerType, TSubclassOf<class USceneComponent> ComponentClass = nullptr);

    /**
     * 设置分割线渲染组件类型
     * @param ComponentClass 分割线渲染组件类型
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    void SetDividerRenderComponentClass(TSubclassOf<class USceneComponent> ComponentClass);

    /**
     * 设置墙壁渲染组件类型
     * @param ComponentClass 墙壁渲染组件类型
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    void SetWallRenderComponentClass(TSubclassOf<class USceneComponent> ComponentClass);

    /**
     * 启用/禁用分割线渲染
     * @param bEnable 是否启用
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    void EnableDividerRendering(bool bEnable = true);

    /**
     * 启用/禁用墙壁渲染
     * @param bEnable 是否启用
     */
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    void EnableWallRendering(bool bEnable = true);

private:
    // ========== 私有成员变量 ==========
    // 注意：这些指针不使用UPROPERTY以避免循环依赖，在构造函数中手动管理生命周期

    UPhysicalLayerGenerator* PhysicalGenerator;
    UBiomeMapper* BiomeMapper;
    UNarrativeLayerGenerator* NarrativeGenerator;
    URoadGenerator* RoadGenerator;
    ULayerScalingSystem* ScalingSystem;
    UUndergroundSystem* UndergroundSystem;
    UDynamicChangeSystem* DynamicSystem;
    UPerformanceOptimizer* PerformanceOptimizer;
    class UMapVisualizationSystem* VisualizationSystem;

    UPROPERTY()
    bool bIsGenerating = false;

    // ========== 私有辅助函数 ==========
    
    // 初始化子系统
    void InitializeSubsystems();
    
    // 执行生成阶段
    bool ExecuteGenerationPhase(EMapGenerationPhase Phase, FMapGenerationResult& Result, 
                               const FMapGenerationParams& Params);
    
    // 更新生成进度
    void UpdateGenerationProgress(EMapGenerationPhase Phase, float Progress);
    
    // 应用层级特定的后处理
    void ApplyLayerSpecificPostProcessing(FMapGenerationResult& Result, const FMapGenerationParams& Params);
    
    // 验证生成结果
    bool ValidateGenerationResult(const FMapGenerationResult& Result);
    
public:
    // 获取格子索引
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    static int32 GetCellIndex(int32 X, int32 Y, int32 Width);

    // 检查坐标有效性
    UFUNCTION(BlueprintCallable, Category = "Map Generator")
    static bool IsValidCoordinate(int32 X, int32 Y, int32 Width, int32 Height);

    // 注意：索引到坐标转换请使用UE内置的 UKismetMathLibrary::Convert1DTo2D

private:
};
