// Copyright Epic Games, Inc. All Rights Reserved.

namespace UnrealBuildTool.Rules
{
	public class MeshResizingEngine : ModuleRules
	{
		public MeshResizingEngine(ReadOnlyTargetRules Target) : base(Target)
		{
			bTreatAsEngineModule = true;
			PublicDependencyModuleNames.AddRange(
				new string[]
				{
				}
			);
			PrivateDependencyModuleNames.AddRange(
				new string[]
				{	
				}
			);
		}
	}
}
