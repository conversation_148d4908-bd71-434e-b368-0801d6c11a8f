{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "CineCameraRigs", "Description": "Extended camera rigs for cinematic workflow", "Category": "Virtual Production", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": true, "Installed": false, "Modules": [{"Name": "CineCameraRigs", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "CineCameraRigsEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit"}], "Plugins": [{"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "ConcertSyncCore", "Enabled": true}, {"Name": "SequencerScripting", "Enabled": true}, {"Name": "LevelSequenceEditor", "Enabled": true}]}