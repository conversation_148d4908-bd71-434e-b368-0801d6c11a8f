<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>flite</Name>
  <Location>//UE5/Main/Engine/Plugins/Experimental/TextToSpeech/Source/ThirdParty/Flite</Location>
  <Function>As part of implementing a cross-platform custom screen reader for the engine, we need a solution for cross platform text to speech (TTS) for various platforms that don�t have a TTS API or accessibility API. This library is that solution.</Function>
  <Eula>https://github.com/festvox/flite/blob/master/COPYING</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>//UE5/Main/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
