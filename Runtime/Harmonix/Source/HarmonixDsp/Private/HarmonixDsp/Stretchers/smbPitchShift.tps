<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>SmbPitchShifter</Name>
  <Location>Engine/Restricted/NotForLicensees/Plugins/Harmonix/Source/HarmonixDsp/Private/HarmonixDsp/Stretchers/SmbPitchShifter.*</Location>
  <Function>Time stretching of audio. Used in various ways for time stretching of audio. Heavily modified from original, as original was really just an example 'c' implementation of a technique.</Function>
  <Eula>http://blogs.zynaptiq.com/bernsee/repo/smbPitchShift.cpp</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>Engine/Restricted/NotForLicensees/Plugins/Harmonix/Source/HarmonixDsp/Licenses</LicenseFolder>
</TpsData>