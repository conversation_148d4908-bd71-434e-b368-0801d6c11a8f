// Copyright Epic Games, Inc. All Rights Reserved.

#include "IComposure.h"

#include "ComposureInternals.h"


DEFINE_LOG_CATEGORY(Composure);


class FComposure : public IComposure
{
	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;
};

IMPLEMENT_MODULE( FComposure, Composure )


void FComposure::StartupModule()
{
	// This code will execute after your module is loaded into memory (but after global variables are initialized, of course.)
}


void FComposure::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.
}
